spring.application.name=Stock
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=**************************************
spring.datasource.username=postgres
spring.datasource.password=58244524
spring.jpa.properties.hibernate.transaction.coordinator_class=jdbc
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.open-in-view=false
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
server.port=8082