package com.example.Stock.service;

import com.example.Stock.entity.Category;
import com.example.Stock.entity.Product;
import com.example.Stock.entity.Tag;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service interface pour gérer les produits dans le système d'inventaire.
 * Fournit des opérations métier avancées adaptées à une gestion professionnelle.
 */
public interface ProductService {

    /**
     * Crée un nouveau produit.
     * @param product le produit à créer
     * @return le produit créé avec ID généré
     * @throws IllegalArgumentException si un produit avec le même nom ou code barre existe déjà
     */
    Product createProduct(Product product);

    /**
     * Met à jour un produit existant.
     * @param productId l'UUID du produit à modifier
     * @param product données mises à jour du produit
     * @return le produit mis à jour
     * @throws ResourceNotFoundException si aucun produit trouvé avec cet ID
     * @throws IllegalArgumentException si le nom ou code barre est déjà utilisé par un autre produit
     */
    Product updateProduct(UUID productId, Product product);

    /**
     * Supprime un produit identifié par son UUID.
     * @param productId l’UUID du produit
     * @throws ResourceNotFoundException si le produit n’existe pas
     */
    void deleteProduct(UUID productId);

    /**
     * Récupère un produit par son identifiant UUID.
     * @param productId UUID du produit
     * @return produit trouvé
     * @throws ResourceNotFoundException si produit introuvable
     */
    Product getProductById(UUID productId);

    /**
     * Cherche un produit par son nom exact.
     * @param name nom exact
     * @return Optional du produit s’il existe
     */
    Optional<Product> getProductByName(String name);

    /**
     * Cherche un produit par code barre.
     * @param barcode code barre exact
     * @return Optional du produit s’il existe
     */
    Optional<Product> getProductByBarcode(String barcode);

    /**
     * Liste tous les produits triés par nom ascendant.
     * @return liste complète
     */
    List<Product> listAllProductsSortedByName();

    /**
     * Recherche de produits où le nom contient la chaîne spécifiée, insensitive case.
     * @param partialName fragment à rechercher
     * @return liste des produits correspondants
     */
    List<Product> searchProductsByNameContaining(String partialName);

    /**
     * Recherche tous les produits d'une catégorie donnée (nom catégorie).
     * @param categoryName nom de la catégorie
     * @return liste des produits dans la catégorie
     */
    List<Product> findProductsByCategoryName(String categoryName);

    /**
     * Recherche tous les produits d'une catégorie via UUID.
     * @param categoryId UUID de la catégorie
     * @return liste des produits
     */
    List<Product> findProductsByCategoryId(UUID categoryId);

    /**
     * Recherche des produits liés à un tag spécifique.
     * @param tag Tag associé
     * @return liste des produits
     */
    List<Product> findProductsByTag(Tag tag);

    /**
     * Recherche des produits par unité de mesure.
     * @param unit unité (ex : “kg”, “litre”)
     * @return liste correspondante
     */
    List<Product> findProductsByUnit(String unit);

    /**
     * Vérifie si un produit existe par nom exact.
     * @param name nom
     * @return true s’il existe
     */
    boolean existsByName(String name);

    /**
     * Vérifie si un produit existe par code barre.
     * @param barcode code barre
     * @return true s’il existe
     */
    boolean existsByBarcode(String barcode);

    /**
     * Liste des produits qui ont du stock dans au moins un emplacement.
     * @return liste des produits stockés
     */
    List<Product> findProductsWithStock();

    /**
     * Liste des produits sans stock ou stock à zéro.
     * @return liste des produits en rupture
     */
    List<Product> findProductsWithoutStock();

    /**
     * Recherche avancée sur nom et description avec recherche insensible à la casse.
     * @param searchText texte à rechercher
     * @return liste des produits correspondants
     */
    List<Product> searchProductsByNameOrDescription(String searchText);
}
