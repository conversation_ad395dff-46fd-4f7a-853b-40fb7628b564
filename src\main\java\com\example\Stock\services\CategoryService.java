package com.example.Stock.services;

import java.util.List;
import java.util.Locale.Category;

import org.springframework.stereotype.Service;

import java.util.UUID;
@Service
public interface CategoryService {
    Category saveCategory(Category category);
    Category updateCategory(Category category);
    Category getCategoryByName(String name);
    void deleteCategory(UUID categoryId);
    void deleteCategory(Category category);
    Category getCategoryById(UUID categoryId);
    List<Category> getAllCategories();
    List<Category> searchCategories(String query);
}

